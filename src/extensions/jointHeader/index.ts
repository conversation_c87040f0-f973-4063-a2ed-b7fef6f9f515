import { mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'
import { Level } from 'docx'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setJointHeader: {
      setJointHeader: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'jointHeader',
  group: 'block',
  atom: true,
  content: 'paragraph',
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      level: {
        default: 1,
      },
      jointHeight: {
        default: null,
      },
      jointHeaderUrl: {
        default: null,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'jointHeader' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['jointHeader', mergeAttributes(HTMLAttributes), 0]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component)
  },
  addCommands() {
    return {
      setJointHeader:
        (options) =>
        ({ commands, tr, dispatch, editor }) => {
          if (dispatch) {
            tr.replaceRange<PERSON>ith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                options,
                editor.schema.nodes.paragraph.create(
                  '',
                  editor.schema.text(options.title || '节头'),
                ),
              ),
            )
          }
          return true
        },
    }
  },
})
