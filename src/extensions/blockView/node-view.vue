<template>
  <node-view-wrapper :id="node.attrs.id" class="umo-node-view"
    :style="`margin-top:${node.attrs.fitView ? '-20px' : '20px'} `" @mouseenter="showChild"
    @mouseleave="cannelHideLayer">
    <div class="umo-node-view__content" :style="styled">
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1" @click="openDialogEdit">
              <div style="display: flex; align-items: center">
                <SettingIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2" @click="handleDelNode">
              <div style="display: flex; align-items: center">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.remove')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>

      <node-view-content />
    </div>

    <modal v-model:visible="dialogVisible" attach="body" :header="t('insert.block.text')" width="1100"
      :close-on-overlay-click="false" @confirm="updateBlock">
      <div>
        <t-form :data="formData" label-align="top">
          <div class="background-bg">
            <div class="background-bg-title">{{ t('insert.block.backgroundImage') }}</div>
            <t-form-item :label="t('insert.block.backgroundImage')" name="backgroundImage">
              <div class="blockRow">
                <div>
                  <t-button @click="backgroundUpload('backgroundImage')">{{ t('insert.block.uploadText') }}</t-button>
                </div>

                <div v-if="formData.backgroundImage" class="block-img" @mousedown="backgroundImageShow = true"
                  @mouseleave="backgroundImageShow = false">
                  <img :src="formData.backgroundImage" object-fit="contain" />
                  <div v-show="backgroundImageShow" class="block-delect" @click="backgroundDelclick">
                    <DeleteIcon color="#fff" />
                  </div>
                </div>
              </div>
            </t-form-item>

            <t-form-item :label="t('insert.block.backgroundColor')" name="backgroundColor">
              <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                  <div
                    :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.backgroundColor};margin-right:10px;`"
                    :class="`${formData.backgroundColor == 'transparent' ? 'transparent' : ''}`"></div>
                  <div class="umo-color-picker-more-menu"
                    :style="`border-bottom: 3px solid ${formData.backgroundColor};}`">
                    <span v-text="t('insert.block.backgroundColor')"></span>
                  </div>
                </div>
                <template #content>
                  <div style="padding: 10px">
                    <color-picker :default-color="defaultColor"
                      @change="borderColorChange($event, 'backgroundColor')" />
                  </div>
                </template>
              </t-popup>

              <!-- <t-color-picker
                v-model="formData.backgroundColor"
                :color-modes="['monochrome']"
              /> -->
            </t-form-item>

            <t-form-item :label="t('insert.block.backgroundImageSetting')" name="backgroundSize" class="settingBg">
              <t-radio-group v-model="formData.backgroundSize" @change="onChange">
                <t-radio value="none" :disabled="!formData.backgroundImage">{{
                  t('insert.block.remove')
                }}</t-radio>
                <t-radio value="cover" :disabled="!formData.backgroundImage">{{
                  t('insert.block.cover')
                }}</t-radio>
                <t-radio value="contain" :disabled="!formData.backgroundImage">{{ t('insert.block.adjust') }}</t-radio>
                <t-radio value="100% 100%" :disabled="!formData.backgroundImage">{{ t('insert.block.stretch')
                }}</t-radio>
                <t-radio value="repeat" :disabled="!formData.backgroundImage">{{
                  t('insert.block.tile')
                }}</t-radio>
                <t-radio value="100% auto" :disabled="!formData.backgroundImage">{{ t('insert.block.widthStretching')
                }}</t-radio>
                <t-radio value="auto 100%" :disabled="!formData.backgroundImage">{{ t('insert.block.heightStretching')
                }}</t-radio>
                <t-radio value="auto" :disabled="!formData.backgroundImage">{{
                  t('insert.block.auto')
                }}</t-radio>
              </t-radio-group>
            </t-form-item>
            <t-form-item :label="t('insert.block.backgroundRepeat')" name="backgroundRepeat">
              <t-radio-group v-model="formData.backgroundRepeat">
                <t-radio value="repeat" :disabled="!formData.backgroundImage">{{
                  t('insert.block.repeat')
                }}</t-radio>
                <t-radio value="repeat-x" :disabled="!formData.backgroundImage">{{ t('insert.block.repeatX')
                }}</t-radio>
                <t-radio value="repeat-y" :disabled="!formData.backgroundImage">{{ t('insert.block.repeatY')
                }}</t-radio>
                <t-radio value="no-repeat" :disabled="!formData.backgroundImage">{{ t('insert.block.noRepeat')
                }}</t-radio>
              </t-radio-group>
            </t-form-item>
            <t-form-item :label="t('insert.block.backgroundImagePosition')" name="backgroundPosition" class="settingBg">
              <t-radio-group v-model="formData.backgroundPosition">
                <t-radio value="left top" :disabled="!formData.backgroundImage">{{ t('insert.block.leftTop')
                }}</t-radio>
                <t-radio value="left center" :disabled="!formData.backgroundImage">{{ t('insert.block.leftCenter')
                }}</t-radio>
                <t-radio value="left bottom" :disabled="!formData.backgroundImage">{{ t('insert.block.leftBottom')
                }}</t-radio>
                <t-radio value="center top" :disabled="!formData.backgroundImage">{{ t('insert.block.centerTop')
                }}</t-radio>
                <t-radio value="center" :disabled="!formData.backgroundImage">{{
                  t('insert.block.center')
                }}</t-radio>
                <t-radio value="center bottom" :disabled="!formData.backgroundImage">{{ t('insert.block.centerBottom')
                }}</t-radio>
                <t-radio value="right top" :disabled="!formData.backgroundImage">{{ t('insert.block.rightTop')
                }}</t-radio>
                <t-radio value="right center" :disabled="!formData.backgroundImage">{{ t('insert.block.rightCenter')
                }}</t-radio>
                <t-radio value="right bottom" :disabled="!formData.backgroundImage">{{ t('insert.block.rightBottom')
                }}</t-radio>
              </t-radio-group>
            </t-form-item>
            <t-form-item :label="t('insert.block.fitView')" name="fitView">
              <t-switch v-model="formData.fitView" />
            </t-form-item>
          </div>

          <div class="border-bg">
            <div class="border-bg-title">{{ t('insert.block.borderStyle') }}</div>
            <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }" placement="right"
              show-arrow>
              <div class="border-bg-icon" @click="borderClick">
                <LinkIcon size="30" :color="borderStyleValue ? '#1890ff' : ''" />
              </div>
            </t-tooltip>
            <div class="border-item">
              <div class="border-item-top">{{ t('insert.block.borderTop') }}
                <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }" placement="right"
                  show-arrow>
                  <VerifiedIcon size="20" />
                </t-tooltip>
              </div>
              <t-form-item :label="t('insert.block.borderWidth')" name="borderTopWidth">
                <t-input-number v-model="formData.borderTopWidth" :min="0" />
                <span style="padding: 0 20px">px</span>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderColor')" name="borderColor">
                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                  <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                    <div
                      :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.borderTopColor};margin-right:10px;`"
                      :class="`${formData.borderTopColor == 'transparent' ? 'transparent' : ''}`"></div>

                    <div class="umo-color-picker-more-menu"
                      :style="`border-bottom: 3px solid ${formData.borderTopColor};}`">
                      <span v-text="t('insert.block.borderColor')"></span>
                    </div>
                  </div>
                  <template #content>
                    <div style="padding: 10px">
                      <color-picker :default-color="defaultColor" @change="borderColorChange($event, 'top')" />
                    </div>
                  </template>
                </t-popup>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderStyle')" name="borderStyle">
                <t-radio-group v-model="formData.borderTopStyle">
                  <t-radio value="dotted" :disabled="borderShow">{{
                    t('insert.block.dotted')
                  }}</t-radio>
                  <t-radio value="solid" :disabled="borderShow">{{
                    t('insert.block.solid')
                  }}</t-radio>
                  <t-radio value="double" :disabled="borderShow">{{
                    t('insert.block.double')
                  }}</t-radio>
                  <t-radio value="dashed" :disabled="borderShow">{{
                    t('insert.block.dashed')
                  }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>
            <div class="border-item">
              <div class="border-item-top">{{ t('insert.block.borderBottom') }}</div>
              <t-form-item :label="t('insert.block.borderWidth')" name="borderWidth">
                <t-input-number v-model="formData.borderBottomWidth" :min="0" />
                <span style="padding: 0 20px">px</span>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderColor')" name="borderColor">
                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                  <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                    <div
                      :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.borderBottomColor};margin-right:10px;`"
                      :class="`${formData.borderBottomColor == 'transparent' ? 'transparent' : ''}`"></div>
                    <div class="umo-color-picker-more-menu"
                      :style="`border-bottom: 3px solid ${formData.borderBottomColor};}`">
                      <span v-text="t('insert.block.borderColor')"></span>
                    </div>
                  </div>
                  <template #content>
                    <div style="padding: 10px">
                      <color-picker :default-color="defaultColor" @change="borderColorChange($event, 'bottom')" />
                    </div>
                  </template>
                </t-popup>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderStyle')" name="borderStyle">
                <t-radio-group v-model="formData.borderBottomStyle">
                  <t-radio value="dotted" :disabled="borderShow">{{
                    t('insert.block.dotted')
                  }}</t-radio>
                  <t-radio value="solid" :disabled="borderShow">{{
                    t('insert.block.solid')
                  }}</t-radio>
                  <t-radio value="double" :disabled="borderShow">{{
                    t('insert.block.double')
                  }}</t-radio>
                  <t-radio value="dashed" :disabled="borderShow">{{
                    t('insert.block.dashed')
                  }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>

            <div class="border-item">
              <div class="border-item-top">{{ t('insert.block.borderLeft') }}</div>
              <t-form-item :label="t('insert.block.borderWidth')" name="borderWidth">
                <t-input-number v-model="formData.borderLeftWidth" :min="0" />
                <span style="padding: 0 20px">px</span>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderColor')" name="borderColor">
                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                  <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                    <div
                      :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.borderLeftColor};margin-right:10px;`"
                      :class="`${formData.borderLeftColor == 'transparent' ? 'transparent' : ''}`"></div>
                    <div class="umo-color-picker-more-menu"
                      :style="`border-bottom: 3px solid ${formData.borderLeftColor};}`">
                      <span v-text="t('insert.block.borderColor')"></span>
                    </div>
                  </div>
                  <template #content>
                    <div style="padding: 10px">
                      <color-picker :default-color="defaultColor" @change="borderColorChange($event, 'left')" />
                    </div>
                  </template>
                </t-popup>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderStyle')" name="borderStyle">
                <t-radio-group v-model="formData.borderLeftStyle">
                  <t-radio value="dotted" :disabled="borderShow">{{
                    t('insert.block.dotted')
                  }}</t-radio>
                  <t-radio value="solid" :disabled="borderShow">{{
                    t('insert.block.solid')
                  }}</t-radio>
                  <t-radio value="double" :disabled="borderShow">{{
                    t('insert.block.double')
                  }}</t-radio>
                  <t-radio value="dashed" :disabled="borderShow">{{
                    t('insert.block.dashed')
                  }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>

            <div class="border-item">
              <div class="border-item-top">{{ t('insert.block.borderRight') }}</div>
              <t-form-item :label="t('insert.block.borderWidth')" name="borderWidth">
                <t-input-number v-model="formData.borderRightWidth" :min="0" />
                <span style="padding: 0 20px">px</span>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderColor')" name="borderColor">
                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                  <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                    <div
                      :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.borderRightColor};margin-right:10px;`"
                      :class="`${formData.borderRightColor == 'transparent' ? 'transparent' : ''}`"></div>
                    <div class="umo-color-picker-more-menu"
                      :style="`border-bottom: 3px solid ${formData.borderRightColor};}`">
                      <span v-text="t('insert.block.borderColor')"></span>
                    </div>
                  </div>
                  <template #content>
                    <div style="padding: 10px">
                      <color-picker :default-color="defaultColor" @change="borderColorChange($event, 'right')" />
                    </div>
                  </template>
                </t-popup>
              </t-form-item>

              <t-form-item :label="t('insert.block.borderStyle')" name="borderStyle">
                <t-radio-group v-model="formData.borderRightStyle">
                  <t-radio value="dotted" :disabled="borderShow">{{
                    t('insert.block.dotted')
                  }}</t-radio>
                  <t-radio value="solid" :disabled="borderShow">{{
                    t('insert.block.solid')
                  }}</t-radio>
                  <t-radio value="double" :disabled="borderShow">{{
                    t('insert.block.double')
                  }}</t-radio>
                  <t-radio value="dashed" :disabled="borderShow">{{
                    t('insert.block.dashed')
                  }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>
          </div>
          <div class="raduis-bg">
            <div class="raduis-bg-title">{{ t('insert.block.borderRadius') }}</div>
            <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }" placement="right"
              show-arrow>
              <div class="raduis-bg-icon" @click="raduisClick">
                <LinkIcon size="30" :color="raduisLinkValue ? '#1890ff' : ''" />
              </div>
            </t-tooltip>


            <t-form-item :label="t('insert.block.borderRadiusTopLeft')" name="borderRadiusTopLeft">
              <div style="display: flex;">
                <t-input-number v-model="formData.borderRadiusTopLeft" :min="0" />
                <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }" placement="right"
                  show-arrow>
                  <VerifiedIcon size="20" style="margin:5px 10px 0" />
                </t-tooltip>
              </div>
            </t-form-item>




            <t-form-item :label="t('insert.block.borderRadiusBottomLeft')" name="borderRadiusBottomLeft">
              <t-input-number v-model="formData.borderRadiusBottomLeft" :min="0" />
            </t-form-item>

            <t-form-item :label="t('insert.block.borderRadiusTopRight')" name="borderRadiusTopRight">
              <t-input-number v-model="formData.borderRadiusTopRight" :min="0" />
            </t-form-item>

            <t-form-item :label="t('insert.block.borderRadiusBottomRight')" name="borderRadiusBottomRight">
              <t-input-number v-model="formData.borderRadiusBottomRight" :min="0" />
            </t-form-item>
          </div>
          <div class="padding-bg">
            <div class="padding-bg-title">{{ t('insert.block.padding') }}</div>
            <div class="padding-bg-icon">
              <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }" placement="right"
                show-arrow>
                <div class="raduis-bg-icon" @click="paddingClick">
                  <LinkIcon size="30" :color="paddingLinkValue ? '#1890ff' : ''" />
                </div>
              </t-tooltip>
            </div>
            <t-form-item :label="t('insert.block.paddingTop')" name="paddingTop">
              <div style="display: flex;">
                <t-input-number v-model="formData.paddingTop" :min="0" />
                <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }" placement="right"
                  show-arrow>
                  <VerifiedIcon size="20" style="margin:5px 10px 0" />
                </t-tooltip>
              </div>

            </t-form-item>

            <t-form-item :label="t('insert.block.paddingRight')" name="paddingRight">
              <t-input-number v-model="formData.paddingRight" :min="0" />
            </t-form-item>

            <t-form-item :label="t('insert.block.paddingBottom')" name="paddingBottom">
              <t-input-number v-model="formData.paddingBottom" :min="0" />
            </t-form-item>

            <t-form-item :label="t('insert.block.paddingLeft')" name="paddingLeft">
              <t-input-number v-model="formData.paddingLeft" :min="0" />
            </t-form-item>
          </div>
        </t-form>
      </div>
    </modal>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, EllipsisIcon, LinkIcon, SettingIcon, VerifiedIcon } from 'tdesign-icons-vue-next'

import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor } = useStore()
let dialogVisible = $ref(false)
const formData = ref({})
const defaultColor = ref('transparent')
const backgroundImageShow = $ref(false)
const borderimgShow = $ref(false)
const styled = computed(() => {
  return {
    background: `url(${node.attrs.backgroundImage}) ${node.attrs.backgroundColor}`,
    'background-size': node.attrs.backgroundSize || 'auto',
    'background-position': node.attrs.backgroundPosition || 'center',
    'background-repeat': node.attrs.backgroundRepeat || 'no-repeat',
    border: `${node.attrs.borderWidth || 0}px ${node.attrs.borderStyle || 'solid'} ${node.attrs.borderColor || 'transparent'}`,
    'border-radius': `${node.attrs.borderRadius}px` || '0',
    'padding-left': `${node.attrs.paddingLeft}px` || '0',
    'padding-right': `${node.attrs.paddingRight}px` || '0',
    'padding-top': `${node.attrs.paddingTop}px` || '0',
    'padding-bottom': `${node.attrs.paddingBottom}px` || '0',
    'border-top-width': `${node.attrs.borderTopWidth}px` || '0',
    'border-right-width': `${node.attrs.borderRightWidth}px` || '0',
    'border-bottom-width': `${node.attrs.borderBottomWidth}px` || '0',
    'border-left-width': `${node.attrs.borderLeftWidth}px` || '0',
    'border-top-style': node.attrs.borderTopStyle || 'solid',
    'border-right-style': node.attrs.borderRightStyle || 'solid',
    'border-bottom-style': node.attrs.borderBottomStyle || 'solid',
    'border-left-style': node.attrs.borderLeftStyle || 'solid',
    'border-top-color': node.attrs.borderTopColor || 'transparent',
    'border-right-color': node.attrs.borderRightColor || 'transparent',
    'border-bottom-color': node.attrs.borderBottomColor || 'transparent',
    'border-left-color': node.attrs.borderLeftColor || 'transparent',
    'border-top-left-radius': `${node.attrs.borderRadiusTopLeft}px` || '0',
    'border-bottom-left-radius':
      `${node.attrs.borderRadiusBottomLeft}px` || '0',
    'border-top-right-radius': `${node.attrs.borderRadiusTopRight}px` || '0',
    'border-bottom-right-radius':
      `${node.attrs.borderRadiusBottomRight}px` || '0',
    overflow: 'hidden',
  }
})

let mousemove = $ref(false)
let isMenuActive = $ref(false)
let hideTimer = null
const showChild = () => {
  mousemove = true
  clearTimeout(hideTimer)
}

const keepOpen = () => {
  isMenuActive = true
  clearTimeout(hideTimer)
}

const cannelHideLayer = () => {
  hideTimer = setTimeout(() => {
    if (!isMenuActive) {
      mousemove = false
    }
  }, 200)
}

const handleDelNode = () => {
  deleteNode()
}

const openDialogEdit = () => {
  dialogVisible = true
  formData.value = {
    ...node.attrs,
  }
}

const backgroundUpload = (type) => {
  chooseFile((file) => {
    if (type === 'backgroundImage') {
      formData.value.backgroundImage = file.fileUrl
    }
    if (type === 'borderImage') {
      formData.value.borderImage = file.fileUrl
    }
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const updateBlock = () => {
  console.log('formData', formData.value.backgroundSize)
  if (formData.value.backgroundSize == 'none') {
    formData.value.backgroundImage = ''
  }
  updateAttributes({
    borderStyleValue: borderStyleValue.value,
    paddingLinkValue: paddingLinkValue.value,
    raduisLinkValue: raduisLinkValue.value,
    ...formData.value,
  })
  dialogVisible = false
}

const backgroundDelclick = () => {
  formData.value.backgroundImage = ''
}

const borderColorChange = (value, type) => {
  console.log(value, type)
  switch (type) {
    case 'top':
      formData.value.borderTopColor = value
      break
    case 'right':
      formData.value.borderRightColor = value
      break
    case 'bottom':
      formData.value.borderBottomColor = value
      break
    case 'left':
      formData.value.borderLeftColor = value
      break
    case 'backgroundColor':
      formData.value.backgroundColor = value
      break
    default:
      break
  }
}



// 圆角值联动
const raduisLinkValue = ref(node.attrs.raduisLinkValue)
const raduisClick = () => {
  raduisLinkValue.value = !raduisLinkValue.value
  if (raduisLinkValue.value) {
    useMessage('success', '圆角值已联动')
    watch(() => formData.value.borderRadiusTopLeft, (value) => {
      formData.value.borderRadiusTopRight = value
      formData.value.borderRadiusBottomLeft = value
      formData.value.borderRadiusBottomRight = value
    })
  } else {
    useMessage('error', '圆角值已取消联动')
  }
}



// 内边距联动
const paddingLinkValue = ref(node.attrs.paddingLinkValue)
const paddingClick = () => {
  paddingLinkValue.value = !paddingLinkValue.value
  if (paddingLinkValue.value) {
    useMessage('success', '内边距已联动')
    watch(() => formData.value.paddingTop, (value) => {
      formData.value.paddingLeft = value
      formData.value.paddingRight = value
      formData.value.paddingBottom = value
    })
  } else {
    useMessage('error', '内边距已取消联动')
  }
}


// 边框样式联动
const borderStyleValue = ref(node.attrs.borderStyleValue)
const borderClick = () => {
  borderStyleValue.value = !borderStyleValue.value
  if (borderStyleValue.value) {
    useMessage('success', '边框样式已联动')
    watch(() => formData.value.borderTopWidth, (value) => {
      formData.value.borderRightWidth = value
      formData.value.borderBottomWidth = value
      formData.value.borderLeftWidth = value
    })

    watch(() => formData.value.borderTopColor, (value) => {
      formData.value.borderRightColor = value
      formData.value.borderBottomColor = value
      formData.value.borderLeftColor = value
    })

    watch(() => formData.value.borderTopStyle, (value) => {
      formData.value.borderRightStyle = value
      formData.value.borderBottomStyle = value
      formData.value.borderLeftStyle = value
    })

  } else {
    useMessage('error', '边框样式已取消')
  }
}





</script>

<style lang="less" scoped>
.transparent {
  background:
    linear-gradient(to bottom right,
      transparent 49%,
      #ff0000 49%,
      #ff0000 51%,
      transparent 51%),
    #f0f0f0 !important;
}

.umo-node-view__content {
  width: 100%;
  overflow: hidden;

  ::v-deep(.umo-node-view) {
    margin: 0;
  }

  ::v-deep(.umo-node-view-rc .questions-wrapper .questions-content .question-item) {
    margin-bottom: 0;
  }

  ::v-deep(.column-item) {
    margin: 0 !important;
    padding: 0;
  }



}

.background-bg {
  margin-top: 30px;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e5e5e5;
  position: relative;

  .background-bg-title {
    position: absolute;
    top: -15px;
    left: 10px;
    background: #fff;
    padding: 0 10px;
    color: #333;
    font-size: 16px;
  }



  .blockRow {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .block-img {
      width: 100px;
      height: 80px;
      position: relative;
      cursor: pointer;
      margin-left: 30px;

      img {
        width: 100%;
        height: 100%;
      }

      .block-delect {
        width: 100px;
        height: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }

  .settingTip {
    position: absolute;
    top: -30px;
    left: 110px;
    color: #999;
  }

  .settingBg {
    margin-top: 30px;
  }
}

.border-bg {
  margin-top: 30px;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e5e5e5;
  position: relative;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 10px;

  .border-bg-title {
    position: absolute;
    top: -15px;
    left: 10px;
    background: #fff;
    padding: 0 10px;
    color: #333;
    font-size: 16px;
    font-weight: bold;
  }


  .border-bg-icon {
    position: absolute;
    right: 20px;
    top: 35%;
    transform: rotate(135deg);
    cursor: pointer;
    z-index: 9999;
  }


  .border-item {
    position: relative;

    .border-item-top {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 20px;
    }
  }



  .blockRow {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .block-img {
      width: 100px;
      height: 80px;
      position: relative;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
      }

      .block-delect {
        width: 100px;
        height: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }

  .settingTip {
    padding: 0 20px;

    .title {
      font-weight: bold;
      font-size: 16px;
    }

    .describe {
      margin-top: 10px;
      color: #666666;
    }
  }
}

.padding-bg {
  margin-top: 30px;
  display: flex;
  justify-content: space-around;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e5e5e5;
  position: relative;

  .padding-bg-title {
    position: absolute;
    top: -15px;
    left: 10px;
    background: #fff;
    padding: 0 10px;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    justify-content: flex-start;
  }

  .padding-bg-icon {
    position: absolute;
    right: 20px;
    top: 35%;
    transform: rotate(135deg);
    cursor: pointer;
  }


}

.raduis-bg {
  margin-top: 30px;
  padding: 20px;
  border-radius: 10px;
  display: flex;
  justify-content: space-around;
  border: 1px solid #e5e5e5;
  position: relative;

  .raduis-bg-title {
    position: absolute;
    top: -15px;
    left: 10px;
    background: #fff;
    padding: 0 10px;
    color: #333;
    font-size: 16px;
    font-weight: bold;
  }

  .raduis-bg-icon {
    position: absolute;
    right: 20px;
    top: 35%;
    transform: rotate(135deg);
    cursor: pointer;
  }
}

::v-deep(.umo-form__label .umo-form__label--top) {
  label {
    font-size: 14px;
    color: #666666;
    font-weight: bold;
  }
}

::v-deep(.umo-alert) {
  padding: 3px 10px;
}

::v-deep(.umo-input--auto-width) {
  width: fit-content;
  min-width: 100px;
}
</style>
