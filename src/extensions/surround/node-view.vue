<template>
  <node-view-wrapper :id="node.attrs.id" class="umo-node-view" @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div v-show="mousemove" class="icon" @click.stop="keepOpen">
      <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
        <EllipsisIcon />
        <template #dropdown>
          <t-dropdown-item :value="1">
            <div style="display: flex; align-items: center" @click="openDialog">
              <SettingIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.setting')
              }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="2">
            <div style="display: flex; align-items: center" @click="delView">
              <FileSearchIcon />
              <span style="margin-left: 5px">{{
                t('insert.chapterHead.delete')
              }}</span>
            </div>
          </t-dropdown-item>
        </template>
      </t-dropdown>
    </div>
    <div class="umo-node-view__content">

      <div ref="containerRef" :class="basicFloat" :style="`${basicPadding}`">
        <drager :selected="selected" :boundary="false" :draggable="Boolean(node.attrs.draggable) && !options.document?.readOnly
          " :disabled="options.document?.readOnly" :angle="node.attrs.angle" :left="Number(node.attrs.left)"
          :top="Number(node.attrs.top)" :width="Number(node.attrs.width)" :height="Number(node.attrs.height)"
          :max-width="maxWidth" :max-height="maxHeight" :min-width="14" :min-height="14" :z-index="10" :style="{
            margin: '0 auto',
            overflow: 'hidden',
          }" @rotate="onRotate" @resize="onResize" @drag="onDrag" @click="selected = true">
          <img ref="imageRef" :src="node.attrs.imageUrl" loading="lazy" class="imgCss" @load="onLoad" />
        </drager>

        <div class="basic-title" :style="`color:${node.attrs.color}`">

          <div v-if="node.attrs.isShowNo == 1">{{ node.attrs.number }}</div>
          <div v-if="node.attrs.isShowImageTitle == 1">
            {{ node.attrs.text }}
          </div>
        </div>
      </div>
      <div>
        <node-view-content class="basic-content" />
      </div>
    </div>

    <modal v-model:visible="dialogVisible" attach="body" :header="t('insert.surround.text')"
      :close-on-overlay-click="false" width="600px" @confirm="insertSurround">
      <t-form :data="dataForm" label-align="top">
        <t-form-item :label="t('insert.surround.surroundTitle')" name="text">
          <t-input v-model="dataForm.text" :placeholder="t('insert.surround.titleplaceholder')" />
        </t-form-item>
        <t-form-item :label="t('insert.surround.type')" name="surroundType">
          <t-radio-group v-model="dataForm.surroundType">
            <t-radio v-for="item in surroundList" :key="item.value" :value="item.value">{{ item.label }}</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item :label="t('insert.surround.spacing')" name="spacing">
          <t-input-number v-model="dataForm.spacing"></t-input-number>
        </t-form-item>
        <t-form-item :label="t('insert.surround.isShowImageTitle')" name="isShowImageTitle">
          <t-radio-group v-model="dataForm.isShowImageTitle">
            <t-radio value="1">{{ t('insert.image.yes') }}</t-radio>
            <t-radio value="0">{{ t('insert.image.no') }}</t-radio>
          </t-radio-group>
        </t-form-item>


        <t-form-item :label="t('insert.surround.isShowNo')" name="isShowNo">
          <t-radio-group v-model="dataForm.isShowNo">
            <t-radio :value="1">{{ t('insert.image.yes') }}</t-radio>
            <t-radio :value="0">{{ t('insert.image.no') }}</t-radio>
          </t-radio-group>
        </t-form-item>


        <t-form-item :label="t('insert.surround.color')" name="color">
          <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${dataForm.color || '#333'};margin-right:10px;`">
              </div>
              <div v-text="t('insert.surround.color')"></div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="backgroundColorChange" />
              </div>
            </template>
          </t-popup>
          <!-- <menus-toolbar-base-containerColor /> -->

          <!-- <t-color-picker
            v-model="dataForm.color"
            size="small"
            :color-modes="['monochrome']"
          /> -->
        </t-form-item>
        <t-form-item :label="t('insert.surround.imageUrl')" name="imageUrl">
          <t-button @click="handleChooseFile">{{
            t('insert.surround.uploadImage')
          }}</t-button>
          <div v-if="dataForm.imageUrl" class="block-img" @mousedown="backgroundImageShow = true"
            @mouseleave="backgroundImageShow = false">
            <img :src="dataForm.imageUrl" style="object-fit: contain" />
            <div v-show="backgroundImageShow" class="block-delect" @click="backgroundDelclick">
              <DeleteIcon color="#fff" />
            </div>
          </div>
        </t-form-item>
      </t-form>
    </modal>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import Drager from 'es-drager'
import {
  DeleteIcon,
  EllipsisIcon,
  FileSearchIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next'

import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { pageTemplateId } = useTemplate()
let template = $ref(null)
const containerRef = ref(null)
const selected = $ref(false)
const defaultColor = ref('#333')
let maxWidth = $ref(0)
let maxHeight = $ref(0)
const imageRef = $ref(null)

const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, getCaptionStyle } = useStore()
let dialogVisible = $ref(false)
const formData = $ref({})
let dataForm = $ref({})
let mousemove = $ref(false)
let isMenuActive = $ref(false)
let hideTimer = null
const backgroundImageShow = $ref(false)

const surroundList = [
  {
    value: 'rightTop',
    label: t('insert.surround.rightTop'),
  },
  {
    value: 'leftTop',
    label: t('insert.surround.leftTop'),
  },
]
onMounted(() => {


  if (node.attrs.number == '') {
    getCaptionStyle().then((res) => {
      if (res.imageNumberType == 1) {
        updateAttributes({
          isShowNo: 0,
          number: '图1',
        })
      } else if (res.imageNumberType == 2) {
        updateAttributes({
          isShowNo: 0,
          number: '图1-1',
        })
      } else {
        updateAttributes({
          isShowNo: 0,
        })
      }
    })
  }


  template = pageTemplateId.value
  document.addEventListener('click', handleClickOutside)

})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const handleClickOutside = (e) => {
  if (!e.target.closest('.top-node-mu')) {
    mousemove = false
    isMenuActive = false
  }
}
const cannelHideLayer = () => {
  hideTimer = setTimeout(() => {
    if (!isMenuActive) {
      mousemove = false
    }
  }, 200)
}

const backgroundColorChange = (value) => {
  dataForm.color = value
}

const basicFloat = computed(() => {
  switch (node.attrs.surroundType) {
    case 'rightTop':
      return 'basic-float-right'
    case 'leftTop':
      return 'basic-float-left'
    default:
      return 'basic-float-left'
  }
})

const basicPadding = computed(() => {
  switch (node.attrs.surroundType) {
    case 'rightTop':
      return `margin-left:${node.attrs.spacing}px;margin-bottom:${node.attrs.spacing}px;`
    case 'leftTop':
      return `margin-right:${node.attrs.spacing}px;margin-bottom:${node.attrs.spacing}px;`
    default:
      return 'basic-padding'
  }
})

const showChild = () => {
  mousemove = true
  clearTimeout(hideTimer)
}

const keepOpen = () => {
  isMenuActive = true
  clearTimeout(hideTimer)
}

const handleChooseFile = async () => {
  chooseFile((file) => {
    dataForm.imageUrl = file.fileUrl
  }, {
    optPreChekck: defaultOptPreChekck
  })

  await nextTick()
  const width = imageRef?.clientWidth ?? 1
  const height = imageRef?.clientHeight ?? 1
  updateAttributes({ width, height })
}

const openDialog = () => {
  dataForm = {
    ...node.attrs,
  }
  dialogVisible = true
}

const insertSurround = () => {
  if (!dataForm.color) dataForm.color = defaultColor.value
  console.log(dataForm)
  updateAttributes({
    ...dataForm,
  })
  dialogVisible = false
  setTimeout(() => {
    const width = imageRef?.clientWidth
    const height = imageRef?.clientHeight

    console.log(width, height)
    updateAttributes({ width, height })
  }, 100)
}

const delView = () => {
  deleteNode()
}

const onLoad = async () => {
  if (node.attrs.width === null) {
    const { clientWidth = 1, clientHeight = 1 } = imageRef ?? {}
    const ratio = clientWidth / clientHeight
    maxWidth = containerRef.value?.$el?.clientWidth
    maxHeight = maxWidth / ratio
    updateAttributes({ width: clientWidth })
  }
  if ([null, 'auto', 0].includes(node.attrs.height)) {
    await nextTick()
    const { height } = imageRef?.getBoundingClientRect() ?? {}
    updateAttributes({ height: height.toFixed(2) })
  }
}

const backgroundDelclick = () => {
  dataForm.imageUrl = ''
}

const onResize = ({ width, height }: { width: number; height: number }) => {
  updateAttributes({
    width: width.toFixed(2),
    height: height.toFixed(2),
  })
}

watch(
  () => node.attrs.equalProportion,
  async (equalProportion: boolean) => {
    await nextTick()
    const width = imageRef?.offsetWidth ?? 1
    const height = imageRef?.offsetHeight ?? 1
    updateAttributes({ width, height })
    maxHeight = equalProportion ? maxWidth / (width / height) : 0
  },
)
</script>

<style lang="less" scoped>
.umo-node-view__content {
  width: 100%;
  position: relative;

  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .toolbar {
    position: absolute;
    right: 48px;
    top: 14px;
  }

  .header-title {
    outline: none;
    padding: 0 20px;
    width: 100%;
  }

  .basic-content {
    >* {
      +* {
        margin-top: var(--umo-content-node-bottom);
      }
    }
  }

  .basic-float-right {
    float: right;
    shape-outside: margin-box;
    /* 可选：改善文字环绕效果 */
    clip-path: inset(0 0 0 0);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    top: 0px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
    }

    .basic-title {
      padding: 10px 0;
      color: #333;
      word-break: break-all;
      display: flex;
      align-items: center;
    }
  }

  .basic-float-left {
    float: left;
    shape-outside: margin-box;
    /* 可选：改善文字环绕效果 */
    clip-path: inset(0 0 0 0);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    top: 0px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
    }

    .basic-title {
      padding: 10px 0;
      color: #333;
      word-break: break-all;
      display: flex;
      gap: 10px;
    }
  }
}

.block-img {
  width: 100px;
  height: 100px;
  position: relative;
  cursor: pointer;
  margin-left: 30px;

  img {
    width: 100%;
  }

  .block-delect {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
}
</style>
