import '@tiptap/extension-text-style'

import { Extension } from '@tiptap/core'

export type FontFamilyOptions = {
  /**
   * A list of node names where the font family can be applied.
   * @default ['textStyle']
   * @example ['heading', 'paragraph']
   */
  types: string[],
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    fontFamily: {
      /**
       * Set the font family
       * @param fontFamily The font family
       * @example editor.commands.setFontFamily('Arial')
       */
      setFontFamily: (fontFamily: string) => ReturnType,
      /**
       * Unset the font family
       * @example editor.commands.unsetFontFamily()
       */
      unsetFontFamily: () => ReturnType,
    }
  }
}
const { options: defaultOptions } = useStore()

/**
 * 检测字体是否在编辑器中存在
 * @param fontFamily 字体
 * @returns 
 */
const checkFontFamily = (fontFamily: string) => {
  if (!fontFamily) {
    return false
  }
  return defaultOptions.value.dicts?.fonts?.filter(item => l(item.label) === fontFamily).length > 0
}

/**
 * This extension allows you to set a font family for text.
 * @see https://www.tiptap.dev/api/extensions/font-family
 */
export default Extension.create<FontFamilyOptions>({
  name: 'fontFamily',

  addOptions() {
    return {
      types: ['textStyle'],
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontFamily: {
            default: null,
            parseHTML: element => {
              let fontFamily = element.style.fontFamily
              if (!checkFontFamily(fontFamily)) {
                return null
              }
              return fontFamily
            },
            renderHTML: attributes => {
              let fontFamily = attributes.fontFamily
              if (!fontFamily) {
                return {}
              }
              
              if (!checkFontFamily(fontFamily)) {
                return {}
              }
              return {
                style: `font-family: ${fontFamily}`,
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      setFontFamily: fontFamily => ({ chain }) => {
        return chain()
          .setMark('textStyle', { fontFamily })
          .run()
      },
      unsetFontFamily: () => ({ chain }) => {
        return chain()
          .setMark('textStyle', { fontFamily: null })
          .removeEmptyTextStyle()
          .run()
      },
    }
  },
})
