import { mergeAttributes, Node } from '@tiptap/core'
// import { VueNodeViewRenderer } from '@tiptap/vue-3'

// import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setLayoutColumn: {
      setLayoutColumn: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'columnItem',
  group: 'columnItem',
  content: 'block+',
  isolating: true,
  addAttributes() {
    return {
      id: {
        default: null,
      },
      width: {
        default: null,
      },
      styleText: {
        default: null,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'columnItem' }]
  },
  renderHTML({ HTMLAttributes }) {
    const { width, styleText } = HTMLAttributes
    return [
      'columnItem',
      mergeAttributes(HTMLAttributes, {
        class: 'column-item',
        style: `width:${width ? width : '100'}%;box-sizing: border-box; ${styleText ? styleText : ''} `,
      }),
      0,
    ]
  },
  addCommands() {
    return {
      // 设置段落对齐方式
      setParagraphAlign:
        (options: any) =>
        // console.log('options', options),
        ({ commands }) => {
          console.log('options', options)
          return commands.updateAttributes(this.name, {
            styleText: options.styleText,
          })
        },
    }
  },
})
