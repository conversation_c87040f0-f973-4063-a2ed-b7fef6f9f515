<!-- 折叠 -->
<template>
  <menus-button ico="foldIcon" :text="t('insert.fold.text')" huge @menu-click="showPopup">
  </menus-button>
  <modal v-model:visible="dialogVisible" attach="body" :header="t('insert.fold.text')" width="700"
    :close-on-overlay-click="false" @confirm="insertFold">
    <div>
      <t-form ref="form" :data="formData">

        <t-form-item :label="t('insert.fold.title')" name="title">
          <t-input v-model="formData.title" :placeholder="t('insert.fold.placeholder')"></t-input>
        </t-form-item>

        <t-form-item :label="t('insert.fold.fontSize')" name="fontSize">
          <t-input-number v-model="formData.fontSize" :min="14" :placeholder="t('insert.fold.placeholder')"
            style="width: 200px">
          </t-input-number>
        </t-form-item>

        <t-form-item :label="t('insert.fold.fontFamily')" name="fontFamily">
          <t-select v-model="formData.fontFamily" :placeholder="t('insert.fold.familyPlaceholder')"
            style="width: 300px;" @change="fontFamilyChange">
            <t-option-group v-for="(group, index) in allFonts" :key="index" :label="group.label" :divider="false">
              <t-option v-for="item in group.children" :key="item.value ?? ''" class="umo-font-family-item"
                :value="item.value ?? ''" :label="l(item.label)">
                <span :style="{ fontFamily: item.value ?? 'SimSun' }" v-text="l(item.label)"></span>
              </t-option>
            </t-option-group>
          </t-select>
        </t-form-item>

        <t-form-item :label="t('insert.fold.expand')" name="expandText" style="width: 300px;">
          <t-input v-model="formData.expandText" :placeholder="t('insert.fold.placeholder')"></t-input>
        </t-form-item>

        <t-form-item :label="t('insert.fold.collapseText')" name="collapseText" style="width: 300px;">
          <t-input v-model="formData.collapseText" :placeholder="t('insert.fold.placeholder')"></t-input>
        </t-form-item>

        <t-form-item :label="t('insert.fold.fontColor')" name="fontColor">
          <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.fontColor};margin-right:10px;`">
              </div>
              <div v-text="t('insert.fold.fontColor')"></div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="backgroundColorChange" />
              </div>
            </template>
          </t-popup>
        </t-form-item>

        <t-form-item :label="t('insert.fold.fontWeight')" name="isStrong">
          <t-switch v-model="formData.isStrong"></t-switch>
        </t-form-item>

        <t-form-item :label="t('insert.fold.isOpen')" name="isOpen">
          <t-switch v-model="formData.isOpen"></t-switch>
        </t-form-item>

        <t-form-item :label="t('insert.fold.isBg')" name="isBg">
          <t-switch v-model="formData.isBg"></t-switch>
        </t-form-item>



        <t-form-item :label="t('insert.fold.iconColor')" name="iconColor">
          <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.iconColor};margin-right:10px;`">
              </div>
              <div v-text="t('insert.fold.iconColor')"></div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="iconColorChange" />
              </div>
            </template>
          </t-popup>
        </t-form-item>

        <t-form-item :label="t('insert.fold.iconText')">
          <t-radio-group v-model="formData.iconUrlGroup" size="large" @change="changeIcon">
            <t-radio-button v-for="item in imgList" :key="item.name" :value="item.name" default-value="foldIcon1">
              <img v-if="item.url" :src="item.url" style="
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
              " />
              <span v-if="item.url === ''">
                {{ t('insert.fold.other') }}
              </span>
            </t-radio-button>
          </t-radio-group>
        </t-form-item>



        <t-form-item v-if="showIcon" :label="t('insert.fold.iconText')" name="iconUrl">
          <div style="display: flex">
            <div style="display: flex; justify-content: space-between">
              <t-button @click="handleUploadIcon">{{
                t('insert.fold.uploadIcon')
              }}</t-button>

              <t-alert v-if="!formData.iconUrl" theme="error" style="padding: 5px; margin: 0 10px">{{
                t('insert.fold.uploadIconTips') }}</t-alert>
            </div>
            <div v-if="formData.iconUrl" class="block-img" @mousedown="iconUrlShow = true"
              @mouseleave="iconUrlShow = false">
              <img :src="formData.iconUrl" alt="" width="20" height="20" />
              <div v-show="iconUrlShow" class="block-delect" @click="backgroundDelclick">
                <DeleteIcon color="#fff" />
              </div>
            </div>
          </div>
        </t-form-item>


      </t-form>
    </div>
  </modal>
</template>
<script lang="ts" setup>
let dialogVisible = $ref(false)
import { DeleteIcon } from 'tdesign-icons-vue-next'

import foldIcon1 from '@/assets/icons/fold1.svg'
import foldIcon2 from '@/assets/icons/fold2.svg'
import foldIcon3 from '@/assets/icons/fold3.svg'
import foldIcon4 from '@/assets/icons/fold4.svg'
import foldIcon5 from '@/assets/icons/fold5.svg'
import foldIcon6 from '@/assets/icons/fold6.svg'
// import commentAltDotsIcon from '@/assets/icons/comment-alt-dots.svg'
// import editbubbleIcon from '@/assets/icons/editbubble.svg'
// import interactiveIcon from '@/assets/icons/interactive.svg'
// import searchAltIcon from '@/assets/icons/search-alt.svg'
// import twitchIcon from '@/assets/icons/twitch.svg'
import { chooseFile } from '@/utils/file'

const formData = ref({
  iconUrl: '',
  title: '',
  isOpen: false,
  isBg: true,
  fontColor: '#333',
  fontSize: 14,
  isStrong: false,
  fontfamily: 'SimSun',
  expandText: '展开',
  collapseText: '收起',
  iconColor: '#333',
  iconUrlGroup: 'foldIcon1'
})
const { options, editor } = useStore()
const iconUrlShow = $ref(false)
const showIcon = ref(false)
const showPopup = () => {
  formData.value = {
    iconUrl: '',
    title: '',
    isOpen: false,
    isBg: true,
    fontColor: '#333',
    fontSize: 14,
    isStrong: false,
    expandText: '展开',
    collapseText: '收起',
    iconColor: '#333',
    iconUrlGroup: 'foldIcon1'
  }

  dialogVisible = true
}

const changeIcon = (e) => {
  if (e === 'other') {
    showIcon.value = true
  } else {
    formData.value.iconUrl = e
    showIcon.value = false
  }
}

const imgList = [
  {
    name: 'foldIcon1',
    url: foldIcon1,
  },
  {
    name: 'foldIcon2',
    url: foldIcon2,
  },
  {
    name: 'foldIcon3',
    url: foldIcon3,
  },
  {
    name: 'foldIcon4',
    url: foldIcon4,
  },
  {
    name: 'foldIcon5',
    url: foldIcon5,
  },
  {
    name: 'foldIcon6',
    url: foldIcon6,
  },
  {
    name: 'other',
    url: ''
  }
]


const handleUploadIcon = () => {
  chooseFile((file) => {
    formData.value.iconUrl = file.fileUrl
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const insertFold = () => {
  if (!formData.value.title) return useMessage('error', t(`insert.fold.error`))
  console.log(formData.value)

  const { iconUrlGroup, ...rest } = formData.value
  if (iconUrlGroup === 'other') {
    rest.iconUrl = formData.value.iconUrl
  } else {
    rest.iconUrl = iconUrlGroup
  }
  editor.value
    .chain()
    .focus()
    .setFold({
      ...rest
    })
    .run()
  dialogVisible = false
}

const backgroundDelclick = () => {
  formData.value.iconUrl = ''
}

const backgroundColorChange = (value) => {
  formData.value.fontColor = value
}
const usedFonts = $ref<string[]>([])
const $recent = useState('recent')
const allFonts = computed(() => {
  const all = [
    {
      label: t('base.fontFamily.all'),
      children: options.value.dicts?.fonts ?? [],
    },
  ]
  // 通过字体值获取字体列表
  const getFontsByValues = (values: string[]) => {
    return values.map(
      (item) =>
        options.value.dicts?.fonts.find(
          ({ value }: { value: string }) => value === item,
        ) ?? {
          label: item,
          item,
        },
    )
  }
  if ($recent.value.fonts.length > 0) {
    all.unshift({
      label: t('base.fontFamily.recent'),
      children: getFontsByValues($recent.value.fonts) as any,
    })
  }
  if (usedFonts.length > 0) {
    all.unshift({
      label: t('base.fontFamily.used'),
      children: getFontsByValues(usedFonts) as any,
    })
  }
  return all
})

const fontFamilyChange = (fontFamily: string) => {
  if (fontFamily) {
    formData.value.fontFamily = fontFamily
  }
}

const iconColorChange = (value) => {
  formData.value.iconColor = value
}

</script>
<style lang="less" scoped>
.block-img {
  width: 30px;
  height: 30px;
  position: relative;
  cursor: pointer;
  margin-left: 30px;

  img {
    width: 100%;
    height: 100%;
  }

  .block-delect {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
}
</style>
