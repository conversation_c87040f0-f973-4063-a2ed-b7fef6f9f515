<template>
  <menus-button ico="jointHeader" :text="t('insert.jointHeader.text')" huge @menu-click="submit">
  </menus-button>
</template>
<script setup>
import { getSelectionText } from '@/extensions/selection'
const dialogVisible = ref(false)
const formData = ref({})

const { editor } = useStore()
const submit = () => {
  const test = getSelectionText(editor?.value) || ''
  editor.value
    ?.chain()
    .focus()
    .setJointHeader({
      title: test,
    })
    .run()
  editor.value?.commands.deleteSelectionNode()
  dialogVisible.value = false
}
</script>

<style lang="less" scoped>
.footer-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  button {
    margin-left: 10px;
  }
}
</style>
