<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-12-04 11:24:54
 * @FilePath: \dutp-editor\src\app.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <div class="box">
    <router-view />
  </div>
</template>

<script setup lang="ts"></script>

<style lang="less">
.box {
  height: 100vh;
  width: 100vw;
  box-sizing: border-box;
  position: relative;
  font-size: var(--umo-font-size);
}

html,
body {
  height: 100vh;
  width: 100vw;
  background-color: var(--umo-container-background);
  overflow: hidden;
}

html {
  scrollbar-width: none;
  /* 针对 Firefox */
  -ms-overflow-style: none;
  /* 针对 Internet Explorer 和 Edge */
}

.bellCss {
  //color: #212121;
  line-height: 1.5;

  span {
    ::v-deep(sub) {
      // font-size: 0.35em;
      position: relative;
      top: -0.2em;
      letter-spacing: 0.2em;
    }

  }

  sub {
    // font-size: 0.35em;
    position: relative;
    top: -0.2em;
    letter-spacing: 0.1em;
  }

  sup {
    // font-size: 0.35em;
    letter-spacing: 0.1em;
  }

  em {
    letter-spacing: 0.1em;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.036);
  }
}
</style>
